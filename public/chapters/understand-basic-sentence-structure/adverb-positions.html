<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>副词的位置和作用</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            transition: transform 0.1s ease;
        }
        .card:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6 max-w-7xl mx-auto">

        <!-- 副词位置概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词在句子中的位置</h2>
            <p class="text-gray-700 mb-4">副词在英语句子中的位置非常灵活，不同的位置会产生不同的强调效果和语义。主要有三个基本位置：句首、句中和句末。</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首位置</div>
                    <div class="keyword text-lg mb-1">Carefully, she opened the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkerfəli, ʃi ˈoʊpənd ðə dɔr/</div>
                    <div class="text-gray-700">小心地，她打开了门。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中位置</div>
                    <div class="keyword text-lg mb-1">She carefully opened the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈkerfəli ˈoʊpənd ðə dɔr/</div>
                    <div class="text-gray-700">她小心地打开了门。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末位置</div>
                    <div class="keyword text-lg mb-1">She opened the door carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈoʊpənd ðə dɔr ˈkerfəli/</div>
                    <div class="text-gray-700">她小心地打开了门。</div>
                </div>
            </div>
        </section>

        <!-- 频率副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">频率副词的位置规则</h2>
            <p class="text-gray-700 mb-4">频率副词（always, usually, often, sometimes, rarely, never等）通常位于主动词之前，助动词或be动词之后。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 在be动词之后</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">always + be动词</div>
                    <div class="keyword text-lg mb-1">She is always happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈɔlweɪz ˈhæpi/</div>
                    <div class="text-gray-700">她总是很开心。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">usually + be动词</div>
                    <div class="keyword text-lg mb-1">He is usually late.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈjuʒuəli leɪt/</div>
                    <div class="text-gray-700">他通常迟到。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">never + be动词</div>
                    <div class="keyword text-lg mb-1">They are never bored.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ɑr ˈnevər bɔrd/</div>
                    <div class="text-gray-700">他们从不感到无聊。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 在助动词之后，主动词之前</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词 + 频率副词</div>
                    <div class="keyword text-lg mb-1">I have always loved music.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ˈɔlweɪz lʌvd ˈmjuzɪk/</div>
                    <div class="text-gray-700">我一直热爱音乐。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词 + 频率副词</div>
                    <div class="keyword text-lg mb-1">You should always be careful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ʃʊd ˈɔlweɪz bi ˈkerfəl/</div>
                    <div class="text-gray-700">你应该总是小心。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 在实义动词之前</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">often + 动词</div>
                    <div class="keyword text-lg mb-1">We often go swimming.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ˈɔfən goʊ ˈswɪmɪŋ/</div>
                    <div class="text-gray-700">我们经常去游泳。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">sometimes + 动词</div>
                    <div class="keyword text-lg mb-1">She sometimes works late.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈsʌmtaɪmz wərks leɪt/</div>
                    <div class="text-gray-700">她有时工作到很晚。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">rarely + 动词</div>
                    <div class="keyword text-lg mb-1">He rarely complains.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈrerli kəmˈpleɪnz/</div>
                    <div class="text-gray-700">他很少抱怨。</div>
                </div>
            </div>
        </section>

        <!-- 方式副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">方式副词的位置</h2>
            <p class="text-gray-700 mb-4">方式副词描述动作的执行方式，通常位于动词之后或句末，有时也可以位于句首以示强调。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 动词之后（最常见）</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">She speaks clearly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi spiks ˈklɪrli/</div>
                    <div class="text-gray-700">她说话很清楚。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">He drives carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz ˈkerfəli/</div>
                    <div class="text-gray-700">他开车很小心。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">They work hard.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ wərk hɑrd/</div>
                    <div class="text-gray-700">他们工作很努力。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句首强调</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调</div>
                    <div class="keyword text-lg mb-1">Quietly, he entered the room.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwaɪətli, hi ˈentərd ðə rum/</div>
                    <div class="text-gray-700">悄悄地，他进入了房间。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调</div>
                    <div class="keyword text-lg mb-1">Suddenly, it started raining.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌdənli, ɪt ˈstɑrtəd ˈreɪnɪŋ/</div>
                    <div class="text-gray-700">突然，开始下雨了。</div>
                </div>
            </div>
        </section>

        <!-- 时间副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">时间副词的位置</h2>
            <p class="text-gray-700 mb-4">时间副词可以位于句首、句中或句末，位置的选择取决于想要表达的强调程度。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 句末位置（最常见）</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末时间副词</div>
                    <div class="keyword text-lg mb-1">I will call you tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɪl kɔl ju təˈmɑroʊ/</div>
                    <div class="text-gray-700">我明天给你打电话。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末时间副词</div>
                    <div class="keyword text-lg mb-1">She arrived yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi əˈraɪvd ˈjestərdeɪ/</div>
                    <div class="text-gray-700">她昨天到达了。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末时间副词</div>
                    <div class="keyword text-lg mb-1">We meet every week.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi mit ˈevri wik/</div>
                    <div class="text-gray-700">我们每周见面。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句首强调</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首时间副词</div>
                    <div class="keyword text-lg mb-1">Yesterday, I met an old friend.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈjestərdeɪ, aɪ met ən oʊld frend/</div>
                    <div class="text-gray-700">昨天，我遇到了一个老朋友。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首时间副词</div>
                    <div class="keyword text-lg mb-1">Next week, we will start the project.</div>
                    <div class="text-sm text-gray-600 mb-1">/nekst wik, wi wɪl stɑrt ðə ˈprɑdʒekt/</div>
                    <div class="text-gray-700">下周，我们将开始这个项目。</div>
                </div>
            </div>
        </section>

        <!-- 地点副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">地点副词的位置</h2>
            <p class="text-gray-700 mb-4">地点副词通常位于句末，但也可以位于句首以示强调。当句子中同时有时间和地点副词时，地点副词通常在前。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末地点副词</div>
                    <div class="keyword text-lg mb-1">She lives here.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi lɪvz hɪr/</div>
                    <div class="text-gray-700">她住在这里。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末地点副词</div>
                    <div class="keyword text-lg mb-1">They went outside.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ went ˈaʊtˌsaɪd/</div>
                    <div class="text-gray-700">他们出去了。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首地点副词</div>
                    <div class="keyword text-lg mb-1">Upstairs, you'll find the library.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈʌpˌsterz, jul faɪnd ðə ˈlaɪˌbreri/</div>
                    <div class="text-gray-700">楼上，你会找到图书馆。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">地点 + 时间的顺序</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">I'll meet you there tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪl mit ju ðer təˈmɑroʊ/</div>
                    <div class="text-gray-700">我明天在那里见你。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">She worked at home yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wərkt æt hoʊm ˈjestərdeɪ/</div>
                    <div class="text-gray-700">她昨天在家工作。</div>
                </div>
            </div>
        </section>

        <!-- 程度副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">程度副词的位置</h2>
            <p class="text-gray-700 mb-4">程度副词（very, quite, rather, extremely等）通常位于它们所修饰的形容词或副词之前。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 修饰形容词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 形容词</div>
                    <div class="keyword text-lg mb-1">She is very beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈveri ˈbjutəfəl/</div>
                    <div class="text-gray-700">她非常漂亮。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 形容词</div>
                    <div class="keyword text-lg mb-1">The movie is quite interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmuvi ɪz kwaɪt ˈɪntrəstɪŋ/</div>
                    <div class="text-gray-700">这部电影相当有趣。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 形容词</div>
                    <div class="keyword text-lg mb-1">It's extremely cold today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈstrimli koʊld təˈdeɪ/</div>
                    <div class="text-gray-700">今天极其寒冷。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 修饰副词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 副词</div>
                    <div class="keyword text-lg mb-1">He runs very fast.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi rʌnz ˈveri fæst/</div>
                    <div class="text-gray-700">他跑得非常快。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 副词</div>
                    <div class="keyword text-lg mb-1">She speaks quite clearly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi spiks kwaɪt ˈklɪrli/</div>
                    <div class="text-gray-700">她说话相当清楚。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 副词</div>
                    <div class="keyword text-lg mb-1">They work rather slowly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ wərk ˈræðər ˈsloʊli/</div>
                    <div class="text-gray-700">他们工作相当慢。</div>
                </div>
            </div>
        </section>

        <!-- 否定副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">否定副词的位置和倒装</h2>
            <p class="text-gray-700 mb-4">某些否定副词（never, rarely, seldom, hardly等）位于句首时，需要使用倒装语序。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 正常语序</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">I have never seen this before.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ˈnevər sin ðɪs bɪˈfɔr/</div>
                    <div class="text-gray-700">我以前从未见过这个。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">She rarely goes out.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈrerli goʊz aʊt/</div>
                    <div class="text-gray-700">她很少出去。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">We seldom meet.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ˈseldəm mit/</div>
                    <div class="text-gray-700">我们很少见面。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 倒装语序（句首强调）</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">倒装语序</div>
                    <div class="keyword text-lg mb-1">Never have I seen this before.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnevər hæv aɪ sin ðɪs bɪˈfɔr/</div>
                    <div class="text-gray-700">我以前从未见过这个。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">倒装语序</div>
                    <div class="keyword text-lg mb-1">Rarely does she go out.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrerli dʌz ʃi goʊ aʊt/</div>
                    <div class="text-gray-700">她很少出去。</div>
                </div>
            </div>
        </section>

        <!-- 连接副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">连接副词的位置</h2>
            <p class="text-gray-700 mb-4">连接副词（however, therefore, moreover, furthermore等）可以位于句首、句中或句末，用来连接句子或段落。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 句首位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首连接副词</div>
                    <div class="keyword text-lg mb-1">However, I disagree with you.</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊˈevər, aɪ ˌdɪsəˈgri wɪð ju/</div>
                    <div class="text-gray-700">然而，我不同意你的观点。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首连接副词</div>
                    <div class="keyword text-lg mb-1">Therefore, we must act quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈðerfɔr, wi mʌst ækt ˈkwɪkli/</div>
                    <div class="text-gray-700">因此，我们必须迅速行动。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句中位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中连接副词</div>
                    <div class="keyword text-lg mb-1">I, however, disagree with you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ, haʊˈevər, ˌdɪsəˈgri wɪð ju/</div>
                    <div class="text-gray-700">然而，我不同意你的观点。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中连接副词</div>
                    <div class="keyword text-lg mb-1">We must, therefore, act quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi mʌst, ˈðerfɔr, ækt ˈkwɪkli/</div>
                    <div class="text-gray-700">因此，我们必须迅速行动。</div>
                </div>
            </div>
        </section>

        <!-- 疑问副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">疑问副词的位置</h2>
            <p class="text-gray-700 mb-4">疑问副词（when, where, why, how等）在疑问句中位于句首，在陈述句中的位置较为灵活。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 疑问句中</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">When will you arrive?</div>
                    <div class="text-sm text-gray-600 mb-1">/wen wɪl ju əˈraɪv/</div>
                    <div class="text-gray-700">你什么时候到达？</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">Where do you live?</div>
                    <div class="text-sm text-gray-600 mb-1">/wer du ju lɪv/</div>
                    <div class="text-gray-700">你住在哪里？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">How did you know?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ dɪd ju noʊ/</div>
                    <div class="text-gray-700">你怎么知道的？</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 间接疑问句中</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接疑问句</div>
                    <div class="keyword text-lg mb-1">I wonder when he will arrive.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈwʌndər wen hi wɪl əˈraɪv/</div>
                    <div class="text-gray-700">我想知道他什么时候到达。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接疑问句</div>
                    <div class="keyword text-lg mb-1">Tell me where you live.</div>
                    <div class="text-sm text-gray-600 mb-1">/tel mi wer ju lɪv/</div>
                    <div class="text-gray-700">告诉我你住在哪里。</div>
                </div>
            </div>
        </section>

        <!-- 多个副词的排列顺序 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">多个副词的排列顺序</h2>
            <p class="text-gray-700 mb-4">当句子中有多个副词时，通常遵循：方式 → 地点 → 时间的顺序。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 方式 + 地点 + 时间</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式+地点+时间</div>
                    <div class="keyword text-lg mb-1">She sang beautifully at the concert last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sæŋ ˈbjutəfəli æt ðə ˈkɑnsərt læst naɪt/</div>
                    <div class="text-gray-700">她昨晚在音乐会上唱得很美。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式+地点+时间</div>
                    <div class="keyword text-lg mb-1">He worked hard in the office yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wərkt hɑrd ɪn ði ˈɔfɪs ˈjestərdeɪ/</div>
                    <div class="text-gray-700">他昨天在办公室努力工作。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 频率副词的特殊位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词位置</div>
                    <div class="keyword text-lg mb-1">She always sings beautifully at concerts.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈɔlweɪz sɪŋz ˈbjutəfəli æt ˈkɑnsərts/</div>
                    <div class="text-gray-700">她在音乐会上总是唱得很美。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词位置</div>
                    <div class="keyword text-lg mb-1">He usually works hard in the office.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈjuʒuəli wərks hɑrd ɪn ði ˈɔfɪs/</div>
                    <div class="text-gray-700">他通常在办公室努力工作。</div>
                </div>
            </div>
        </section>

        <!-- 副词位置的语义差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置的语义差异</h2>
            <p class="text-gray-700 mb-4">同一个副词在不同位置可能会产生不同的语义或强调效果。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. only的位置变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰主语</div>
                    <div class="keyword text-lg mb-1">Only John can solve this problem.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊnli dʒɑn kæn sɑlv ðɪs ˈprɑbləm/</div>
                    <div class="text-gray-700">只有约翰能解决这个问题。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰动词</div>
                    <div class="keyword text-lg mb-1">John can only solve this problem.</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɑn kæn ˈoʊnli sɑlv ðɪs ˈprɑbləm/</div>
                    <div class="text-gray-700">约翰只能解决这个问题。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. even的位置变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰主语</div>
                    <div class="keyword text-lg mb-1">Even children understand this.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈivən ˈtʃɪldrən ˌʌndərˈstænd ðɪs/</div>
                    <div class="text-gray-700">连孩子都理解这个。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰动词</div>
                    <div class="keyword text-lg mb-1">Children even understand this.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtʃɪldrən ˈivən ˌʌndərˈstænd ðɪs/</div>
                    <div class="text-gray-700">孩子们甚至理解这个。</div>
                </div>
            </div>
        </section>

        <!-- 副词位置的常见错误 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置的常见错误</h2>
            <p class="text-gray-700 mb-4">学习者在使用副词时经常犯的位置错误及其纠正方法。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 频率副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">I go always to school by bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ goʊ ˈɔlweɪz tu skul baɪ bʌs/</div>
                    <div class="text-gray-700">我总是乘公交车上学。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">I always go to school by bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈɔlweɪz goʊ tu skul baɪ bʌs/</div>
                    <div class="text-gray-700">我总是乘公交车上学。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 程度副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">She is beautiful very.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈbjutəfəl ˈveri/</div>
                    <div class="text-gray-700">她非常漂亮。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">She is very beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈveri ˈbjutəfəl/</div>
                    <div class="text-gray-700">她非常漂亮。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 方式副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">He quickly very runs.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈkwɪkli ˈveri rʌnz/</div>
                    <div class="text-gray-700">他跑得非常快。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">He runs very quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi rʌnz ˈveri ˈkwɪkli/</div>
                    <div class="text-gray-700">他跑得非常快。</div>
                </div>
            </div>
        </section>

        <!-- 特殊副词的位置规则 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">特殊副词的位置规则</h2>
            <p class="text-gray-700 mb-4">某些副词有特殊的位置规则，需要特别注意。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. enough的位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形容词 + enough</div>
                    <div class="keyword text-lg mb-1">She is old enough to drive.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz oʊld ɪˈnʌf tu draɪv/</div>
                    <div class="text-gray-700">她年龄够大可以开车了。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">enough + 名词</div>
                    <div class="keyword text-lg mb-1">We have enough time.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi hæv ɪˈnʌf taɪm/</div>
                    <div class="text-gray-700">我们有足够的时间。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. too的位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">too + 形容词</div>
                    <div class="keyword text-lg mb-1">It's too hot today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts tu hɑt təˈdeɪ/</div>
                    <div class="text-gray-700">今天太热了。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">too + 副词</div>
                    <div class="keyword text-lg mb-1">He drives too fast.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz tu fæst/</div>
                    <div class="text-gray-700">他开车太快了。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末表示"也"</div>
                    <div class="keyword text-lg mb-1">I like coffee too.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔfi tu/</div>
                    <div class="text-gray-700">我也喜欢咖啡。</div>
                </div>
            </div>
        </section>

        <!-- 实用练习示例 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">实用练习示例</h2>
            <p class="text-gray-700 mb-4">通过实际例句练习掌握副词的正确位置。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 日常对话中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常对话</div>
                    <div class="keyword text-lg mb-1">I usually have breakfast at 7 AM.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈjuʒuəli hæv ˈbrekfəst æt ˈsevən eɪ em/</div>
                    <div class="text-gray-700">我通常早上7点吃早餐。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常对话</div>
                    <div class="keyword text-lg mb-1">She carefully locked the door before leaving.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈkerfəli lɑkt ðə dɔr bɪˈfɔr ˈlivɪŋ/</div>
                    <div class="text-gray-700">她离开前小心地锁了门。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 商务场合中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务对话</div>
                    <div class="keyword text-lg mb-1">We need to carefully review the contract.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi nid tu ˈkerfəli rɪˈvju ðə ˈkɑntrækt/</div>
                    <div class="text-gray-700">我们需要仔细审查合同。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务对话</div>
                    <div class="keyword text-lg mb-1">The project is progressing smoothly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑdʒekt ɪz prəˈgresɪŋ ˈsmuðli/</div>
                    <div class="text-gray-700">项目进展顺利。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 学术写作中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作</div>
                    <div class="keyword text-lg mb-1">Furthermore, the research clearly demonstrates...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfərðərˌmɔr, ðə rɪˈsərtʃ ˈklɪrli ˈdemənˌstreɪts/</div>
                    <div class="text-gray-700">此外，研究清楚地表明...</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作</div>
                    <div class="keyword text-lg mb-1">The results were carefully analyzed.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈzʌlts wər ˈkerfəli ˈænəˌlaɪzd/</div>
                    <div class="text-gray-700">结果被仔细分析了。</div>
                </div>
            </div>
        </section>

        <!-- 总结要点 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置要点总结</h2>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">关键记忆点：</h3>
                <ul class="space-y-2 text-gray-700">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>频率副词：be动词后，实义动词前，助动词后</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>方式副词：通常在动词后或句末</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>时间地点副词：地点在前，时间在后</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>程度副词：在所修饰词之前</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>否定副词句首：需要倒装</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>多个副词：方式→地点→时间</span>
                    </li>
                </ul>
            </div>
        </section>

        <!-- 常用副词位置速查表 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">常用副词位置速查</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词</div>
                    <div class="keyword text-base mb-1">always, usually, often</div>
                    <div class="text-sm text-gray-600 mb-1">动词前/be动词后</div>
                    <div class="text-gray-700 text-sm">I always study.</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式副词</div>
                    <div class="keyword text-base mb-1">carefully, quickly</div>
                    <div class="text-sm text-gray-600 mb-1">动词后/句末</div>
                    <div class="text-gray-700 text-sm">He works hard.</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词</div>
                    <div class="keyword text-base mb-1">very, quite, rather</div>
                    <div class="text-sm text-gray-600 mb-1">修饰词前</div>
                    <div class="text-gray-700 text-sm">Very good!</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间副词</div>
                    <div class="keyword text-base mb-1">yesterday, tomorrow</div>
                    <div class="text-sm text-gray-600 mb-1">句首/句末</div>
                    <div class="text-gray-700 text-sm">See you tomorrow.</div>
                </div>
            </div>
        </section>

    </div>
</body>
</html>