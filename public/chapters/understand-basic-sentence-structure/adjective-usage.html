<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>形容词用法</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword { color: #3d74ed; font-weight: bold; }
        .card { transition: none; }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 形容词位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">形容词的位置</h2>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">1. 定语形容词（修饰名词）</h3>
                <p class="mb-4 text-gray-600">形容词通常放在名词前面作定语，修饰名词。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本定语</div>
                        <div class="keyword text-lg mb-1">A beautiful flower</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˈbjuːtɪfəl ˈflaʊər/</div>
                        <div class="text-gray-700">一朵美丽的花</div>
                    </div>
                    
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本定语</div>
                        <div class="keyword text-lg mb-1">A big house</div>
                        <div class="text-sm text-gray-600 mb-1">/ə bɪɡ haʊs/</div>
                        <div class="text-gray-700">一座大房子</div>
                    </div>
                    
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本定语</div>
                        <div class="keyword text-lg mb-1">An old car</div>
                        <div class="text-sm text-gray-600 mb-1">/ən oʊld kɑːr/</div>
                        <div class="text-gray-700">一辆旧车</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">2. 表语形容词（跟在系动词后）</h3>
                <p class="mb-4 text-gray-600">形容词作表语时，放在系动词（be, seem, look, feel等）后面。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">be动词表语</div>
                        <div class="keyword text-lg mb-1">She is happy</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈhæpi/</div>
                        <div class="text-gray-700">她很开心</div>
                    </div>
                    
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">seem表语</div>
                        <div class="keyword text-lg mb-1">He seems tired</div>
                        <div class="text-sm text-gray-600 mb-1">/hi simz taɪərd/</div>
                        <div class="text-gray-700">他看起来很累</div>
                    </div>
                    
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">look表语</div>
                        <div class="keyword text-lg mb-1">The food looks delicious</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə fud lʊks dɪˈlɪʃəs/</div>
                        <div class="text-gray-700">食物看起来很美味</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">3. 后置定语</h3>
                <p class="mb-4 text-gray-600">某些情况下，形容词需要放在名词后面。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合不定代词</div>
                        <div class="keyword text-lg mb-1">Something important</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈsʌmθɪŋ ɪmˈpɔːrtənt/</div>
                        <div class="text-gray-700">重要的事情</div>
                    </div>
                    
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合不定代词</div>
                        <div class="keyword text-lg mb-1">Nothing special</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈnʌθɪŋ ˈspeʃəl/</div>
                        <div class="text-gray-700">没什么特别的</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 形容词顺序 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">形容词的顺序</h2>
            
            <div class="mb-6">
                <p class="mb-4 text-gray-600">当多个形容词修饰同一个名词时，需要按照特定的顺序排列：</p>
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="text-center text-lg font-semibold text-gray-800">
                        <span class="keyword">观点</span> → 
                        <span class="keyword">大小</span> → 
                        <span class="keyword">年龄</span> → 
                        <span class="keyword">形状</span> → 
                        <span class="keyword">颜色</span> → 
                        <span class="keyword">国籍</span> → 
                        <span class="keyword">材料</span> → 
                        <span class="keyword">名词</span>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">顺序示例</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">观点+大小+颜色</div>
                        <div class="keyword text-lg mb-1">A beautiful big red car</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˈbjuːtɪfəl bɪɡ red kɑːr/</div>
                        <div class="text-gray-700">一辆漂亮的大红车</div>
                    </div>
                    
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">观点+年龄+国籍</div>
                        <div class="keyword text-lg mb-1">A lovely old Chinese vase</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˈlʌvli oʊld ˌtʃaɪˈniz veɪs/</div>
                        <div class="text-gray-700">一个可爱的中国古花瓶</div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">完整顺序示例</div>
                        <div class="keyword text-lg mb-1">A beautiful small old round blue Chinese wooden table</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˈbjuːtɪfəl smɔːl oʊld raʊnd blu ˌtʃaɪˈniz ˈwʊdən ˈteɪbəl/</div>
                        <div class="text-gray-700">一张漂亮的中国古式蓝色圆形小木桌</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">各类形容词示例</h3>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                        <div class="text-xs text-gray-500 mb-1">观点</div>
                        <div class="keyword text-sm mb-1">Beautiful</div>
                        <div class="text-xs text-gray-600 mb-1">/ˈbjuːtɪfəl/</div>
                        <div class="text-xs text-gray-700">美丽的</div>
                    </div>
                    
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-3 shadow-sm">
                        <div class="text-xs text-gray-500 mb-1">大小</div>
                        <div class="keyword text-sm mb-1">Large</div>
                        <div class="text-xs text-gray-600 mb-1">/lɑːrdʒ/</div>
                        <div class="text-xs text-gray-700">大的</div>
                    </div>
                    
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm">
                        <div class="text-xs text-gray-500 mb-1">年龄</div>
                        <div class="keyword text-sm mb-1">Ancient</div>
                        <div class="text-xs text-gray-600 mb-1">/ˈeɪnʃənt/</div>
                        <div class="text-xs text-gray-700">古老的</div>
                    </div>
                    
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-3 shadow-sm">
                        <div class="text-xs text-gray-500 mb-1">形状</div>
                        <div class="keyword text-sm mb-1">Round</div>
                        <div class="text-xs text-gray-600 mb-1">/raʊnd/</div>
                        <div class="text-xs text-gray-700">圆的</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 形容词比较级和最高级 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">形容词的比较级和最高级</h2>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">1. 单音节形容词</h3>
                <p class="mb-4 text-gray-600">单音节形容词通常在词尾加-er构成比较级，加-est构成最高级。</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Tall</div>
                        <div class="text-sm text-gray-600 mb-1">/tɔːl/</div>
                        <div class="text-gray-700">高的</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">Taller</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtɔːlər/</div>
                        <div class="text-gray-700">更高的</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Tallest</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtɔːləst/</div>
                        <div class="text-gray-700">最高的</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级句子</div>
                        <div class="keyword text-lg mb-1">He is taller than me</div>
                        <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈtɔːlər ðæn mi/</div>
                        <div class="text-gray-700">他比我高</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级句子</div>
                        <div class="keyword text-lg mb-1">He is the tallest in our class</div>
                        <div class="text-sm text-gray-600 mb-1">/hi ɪz ðə ˈtɔːləst ɪn aʊər klæs/</div>
                        <div class="text-gray-700">他是我们班最高的</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">2. 双音节形容词</h3>
                <p class="mb-4 text-gray-600">以-y结尾的双音节形容词，变y为i再加-er/-est；其他双音节形容词用more/most。</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Happy</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈhæpi/</div>
                        <div class="text-gray-700">快乐的</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">Happier</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈhæpiər/</div>
                        <div class="text-gray-700">更快乐的</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Happiest</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈhæpiəst/</div>
                        <div class="text-gray-700">最快乐的</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Careful</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈkerfəl/</div>
                        <div class="text-gray-700">小心的</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">More careful</div>
                        <div class="text-sm text-gray-600 mb-1">/mɔːr ˈkerfəl/</div>
                        <div class="text-gray-700">更小心的</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Most careful</div>
                        <div class="text-sm text-gray-600 mb-1">/moʊst ˈkerfəl/</div>
                        <div class="text-gray-700">最小心的</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">3. 多音节形容词</h3>
                <p class="mb-4 text-gray-600">三个或以上音节的形容词用more构成比较级，most构成最高级。</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Beautiful</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈbjuːtɪfəl/</div>
                        <div class="text-gray-700">美丽的</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">More beautiful</div>
                        <div class="text-sm text-gray-600 mb-1">/mɔːr ˈbjuːtɪfəl/</div>
                        <div class="text-gray-700">更美丽的</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Most beautiful</div>
                        <div class="text-sm text-gray-600 mb-1">/moʊst ˈbjuːtɪfəl/</div>
                        <div class="text-gray-700">最美丽的</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">4. 不规则形容词</h3>
                <p class="mb-4 text-gray-600">一些形容词的比较级和最高级是不规则变化的，需要特别记忆。</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Good</div>
                        <div class="text-sm text-gray-600 mb-1">/ɡʊd/</div>
                        <div class="text-gray-700">好的</div>
                    </div>

                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">Better</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈbetər/</div>
                        <div class="text-gray-700">更好的</div>
                    </div>

                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Best</div>
                        <div class="text-sm text-gray-600 mb-1">/best/</div>
                        <div class="text-gray-700">最好的</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Bad</div>
                        <div class="text-sm text-gray-600 mb-1">/bæd/</div>
                        <div class="text-gray-700">坏的</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">Worse</div>
                        <div class="text-sm text-gray-600 mb-1">/wɜːrs/</div>
                        <div class="text-gray-700">更坏的</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Worst</div>
                        <div class="text-sm text-gray-600 mb-1">/wɜːrst/</div>
                        <div class="text-gray-700">最坏的</div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原级</div>
                        <div class="keyword text-lg mb-1">Many/Much</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmeni/ /mʌtʃ/</div>
                        <div class="text-gray-700">许多的</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比较级</div>
                        <div class="keyword text-lg mb-1">More</div>
                        <div class="text-sm text-gray-600 mb-1">/mɔːr/</div>
                        <div class="text-gray-700">更多的</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">最高级</div>
                        <div class="keyword text-lg mb-1">Most</div>
                        <div class="text-sm text-gray-600 mb-1">/moʊst/</div>
                        <div class="text-gray-700">最多的</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">5. 比较级和最高级的用法</h3>

                <div class="mb-4">
                    <h4 class="text-md font-semibold mb-2 text-gray-600">比较级用法</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                            <div class="text-sm text-gray-500 mb-1">than比较</div>
                            <div class="keyword text-lg mb-1">This book is more interesting than that one</div>
                            <div class="text-sm text-gray-600 mb-1">/ðɪs bʊk ɪz mɔːr ˈɪntrəstɪŋ ðæn ðæt wʌn/</div>
                            <div class="text-gray-700">这本书比那本书更有趣</div>
                        </div>

                        <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                            <div class="text-sm text-gray-500 mb-1">much修饰比较级</div>
                            <div class="keyword text-lg mb-1">She is much taller than her sister</div>
                            <div class="text-sm text-gray-600 mb-1">/ʃi ɪz mʌtʃ ˈtɔːlər ðæn hər ˈsɪstər/</div>
                            <div class="text-gray-700">她比她姐姐高得多</div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h4 class="text-md font-semibold mb-2 text-gray-600">最高级用法</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                            <div class="text-sm text-gray-500 mb-1">the + 最高级</div>
                            <div class="keyword text-lg mb-1">She is the smartest student in the class</div>
                            <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ðə ˈsmɑːrtəst ˈstudənt ɪn ðə klæs/</div>
                            <div class="text-gray-700">她是班上最聪明的学生</div>
                        </div>

                        <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                            <div class="text-sm text-gray-500 mb-1">one of + 最高级</div>
                            <div class="keyword text-lg mb-1">This is one of the best movies</div>
                            <div class="text-sm text-gray-600 mb-1">/ðɪs ɪz wʌn ʌv ðə best ˈmuviz/</div>
                            <div class="text-gray-700">这是最好的电影之一</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特殊用法和注意事项 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">特殊用法和注意事项</h2>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">1. 同等比较</h3>
                <p class="mb-4 text-gray-600">使用as...as结构表示同等程度的比较。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">肯定同等比较</div>
                        <div class="keyword text-lg mb-1">She is as tall as her brother</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz æz tɔːl æz hər ˈbrʌðər/</div>
                        <div class="text-gray-700">她和她哥哥一样高</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">否定同等比较</div>
                        <div class="keyword text-lg mb-1">This car is not as expensive as that one</div>
                        <div class="text-sm text-gray-600 mb-1">/ðɪs kɑːr ɪz nɑːt æz ɪkˈspensɪv æz ðæt wʌn/</div>
                        <div class="text-gray-700">这辆车没有那辆车贵</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">2. 倍数表达</h3>
                <p class="mb-4 text-gray-600">表示倍数关系的形容词比较。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">倍数 + as...as</div>
                        <div class="keyword text-lg mb-1">This room is twice as big as that one</div>
                        <div class="text-sm text-gray-600 mb-1">/ðɪs rum ɪz twaɪs æz bɪɡ æz ðæt wʌn/</div>
                        <div class="text-gray-700">这个房间是那个房间的两倍大</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">倍数 + 比较级</div>
                        <div class="keyword text-lg mb-1">This box is three times heavier than that one</div>
                        <div class="text-sm text-gray-600 mb-1">/ðɪs bɑːks ɪz θri taɪmz ˈheviər ðæn ðæt wʌn/</div>
                        <div class="text-gray-700">这个盒子比那个盒子重三倍</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-700">3. 常见错误避免</h3>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">错误示例</div>
                        <div class="text-lg mb-1 line-through text-red-600">More better (错误)</div>
                        <div class="keyword text-lg mb-1">Better (正确)</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈbetər/</div>
                        <div class="text-gray-700">更好的（不能重复使用比较级标记）</div>
                    </div>

                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">错误示例</div>
                        <div class="text-lg mb-1 line-through text-red-600">Most tallest (错误)</div>
                        <div class="keyword text-lg mb-1">Tallest (正确)</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtɔːləst/</div>
                        <div class="text-gray-700">最高的（不能重复使用最高级标记）</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
