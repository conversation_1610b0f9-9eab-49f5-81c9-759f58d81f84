<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>冠词系统</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            min-height: 120px;
        }
    </style>
</head>
<body class="bg-white">
    <div class="container mx-auto p-6 max-w-6xl">
        
        <!-- 冠词系统概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">冠词系统基础</h2>
            <p class="text-gray-700 mb-4 leading-7">冠词（Articles）是英语中最基础但也是最重要的语法成分之一。英语中有三个冠词：<span class="keyword">the</span>（定冠词）、<span class="keyword">a</span>（不定冠词）和 <span class="keyword">an</span>（不定冠词）。正确使用冠词是掌握地道美式英语的关键。</p>
            <p class="text-gray-700 leading-7">冠词的选择取决于名词的特定性、可数性以及读音等因素。掌握冠词的使用规则能让你的英语表达更加准确和自然。</p>
        </section>

        <!-- 不定冠词 a/an -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">不定冠词 A/An - 表示"一个"</h3>
            <p class="text-gray-700 mb-4 leading-7">不定冠词 <span class="keyword">a</span> 和 <span class="keyword">an</span> 用于单数可数名词前，表示"一个"的概念，但不强调数量。选择 a 还是 an 取决于后面单词的发音，而不是拼写。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">A 的使用规则</h4>
                <p class="text-gray-700 mb-3 leading-7">当后面的单词以<span class="keyword">辅音音素</span>开头时使用 <span class="keyword">a</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">a book</div>
                        <div class="text-sm text-gray-600 mb-1">/ə bʊk/</div>
                        <div class="text-gray-700">一本书</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">a car</div>
                        <div class="text-sm text-gray-600 mb-1">/ə kɑːr/</div>
                        <div class="text-gray-700">一辆车</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">a dog</div>
                        <div class="text-sm text-gray-600 mb-1">/ə dɔːɡ/</div>
                        <div class="text-gray-700">一只狗</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">a university</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˌjuːnɪˈvɜːrsəti/</div>
                        <div class="text-gray-700">一所大学</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">a European</div>
                        <div class="text-sm text-gray-600 mb-1">/ə ˌjʊrəˈpiːən/</div>
                        <div class="text-gray-700">一个欧洲人</div>
                    </div>
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">a one-way street</div>
                        <div class="text-sm text-gray-600 mb-1">/ə wʌn weɪ striːt/</div>
                        <div class="text-gray-700">一条单行道</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">An 的使用规则</h4>
                <p class="text-gray-700 mb-3 leading-7">当后面的单词以<span class="keyword">元音音素</span>开头时使用 <span class="keyword">an</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">an apple</div>
                        <div class="text-sm text-gray-600 mb-1">/ən ˈæpəl/</div>
                        <div class="text-gray-700">一个苹果</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">an elephant</div>
                        <div class="text-sm text-gray-600 mb-1">/ən ˈeləfənt/</div>
                        <div class="text-gray-700">一头大象</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基本用法</div>
                        <div class="keyword text-lg mb-1">an orange</div>
                        <div class="text-sm text-gray-600 mb-1">/ən ˈɔːrɪndʒ/</div>
                        <div class="text-gray-700">一个橙子</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">an hour</div>
                        <div class="text-sm text-gray-600 mb-1">/ən aʊər/</div>
                        <div class="text-gray-700">一小时</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">an honest person</div>
                        <div class="text-sm text-gray-600 mb-1">/ən ˈɑːnəst ˈpɜːrsən/</div>
                        <div class="text-gray-700">一个诚实的人</div>
                    </div>
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">特殊情况</div>
                        <div class="keyword text-lg mb-1">an umbrella</div>
                        <div class="text-sm text-gray-600 mb-1">/ən ʌmˈbrelə/</div>
                        <div class="text-gray-700">一把雨伞</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 定冠词 the -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">定冠词 The - 表示特指</h3>
            <p class="text-gray-700 mb-4 leading-7">定冠词 <span class="keyword">the</span> 是英语中使用频率最高的单词之一。它用于特指某个特定的人、事物或概念，表示说话者和听话者都知道所指的是什么。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">The 的发音规则</h4>
                <p class="text-gray-700 mb-3 leading-7"><span class="keyword">the</span> 有两种发音，取决于后面单词的发音：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">辅音前发音</div>
                        <div class="keyword text-lg mb-1">the book</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə bʊk/</div>
                        <div class="text-gray-700">这本书</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">元音前发音</div>
                        <div class="keyword text-lg mb-1">the apple</div>
                        <div class="text-sm text-gray-600 mb-1">/ði ˈæpəl/</div>
                        <div class="text-gray-700">这个苹果</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">特指已知事物</h4>
                <p class="text-gray-700 mb-3 leading-7">当谈论双方都知道的特定事物时使用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">上下文特指</div>
                        <div class="keyword text-lg mb-1">I bought a car. The car is red.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ bɔːt ə kɑːr. ði kɑːr ɪz red/</div>
                        <div class="text-gray-700">我买了一辆车。这辆车是红色的。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">环境特指</div>
                        <div class="keyword text-lg mb-1">Please close the door.</div>
                        <div class="text-sm text-gray-600 mb-1">/pliːz kloʊz ðə dɔːr/</div>
                        <div class="text-gray-700">请关门。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">独一无二的事物</h4>
                <p class="text-gray-700 mb-3 leading-7">世界上独一无二的事物前必须用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">天体</div>
                        <div class="keyword text-lg mb-1">the sun</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə sʌn/</div>
                        <div class="text-gray-700">太阳</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">天体</div>
                        <div class="keyword text-lg mb-1">the moon</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə muːn/</div>
                        <div class="text-gray-700">月亮</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地球</div>
                        <div class="keyword text-lg mb-1">the earth</div>
                        <div class="text-sm text-gray-600 mb-1">/ði ɜːrθ/</div>
                        <div class="text-gray-700">地球</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 不定冠词的具体用法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">不定冠词的具体用法</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">首次提及</h4>
                <p class="text-gray-700 mb-3 leading-7">第一次提到某个事物时使用不定冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">首次提及</div>
                        <div class="keyword text-lg mb-1">I saw a movie yesterday.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ sɔː ə ˈmuːvi ˈjestərdeɪ/</div>
                        <div class="text-gray-700">我昨天看了一部电影。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">首次提及</div>
                        <div class="keyword text-lg mb-1">There's a cat in the garden.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðerz ə kæt ɪn ðə ˈɡɑːrdən/</div>
                        <div class="text-gray-700">花园里有一只猫。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">表示职业</h4>
                <p class="text-gray-700 mb-3 leading-7">表示某人的职业时使用不定冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">职业</div>
                        <div class="keyword text-lg mb-1">She is a teacher.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ə ˈtiːtʃər/</div>
                        <div class="text-gray-700">她是一名教师。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">职业</div>
                        <div class="keyword text-lg mb-1">He is an engineer.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi ɪz ən ˌendʒɪˈnɪr/</div>
                        <div class="text-gray-700">他是一名工程师。</div>
                    </div>
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">职业</div>
                        <div class="keyword text-lg mb-1">I want to be a doctor.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ wɑːnt tu bi ə ˈdɑːktər/</div>
                        <div class="text-gray-700">我想成为一名医生。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 定冠词的具体用法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">定冠词的具体用法</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">最高级前必须用 the</h4>
                <p class="text-gray-700 mb-3 leading-7">形容词和副词的最高级前必须使用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">形容词最高级</div>
                        <div class="keyword text-lg mb-1">She is the smartest student.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ðə ˈsmɑːrtəst ˈstuːdənt/</div>
                        <div class="text-gray-700">她是最聪明的学生。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">副词最高级</div>
                        <div class="keyword text-lg mb-1">He runs the fastest.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi rʌnz ðə ˈfæstəst/</div>
                        <div class="text-gray-700">他跑得最快。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">序数词前用 the</h4>
                <p class="text-gray-700 mb-3 leading-7">序数词前通常使用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">序数词</div>
                        <div class="keyword text-lg mb-1">the first day</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə fɜːrst deɪ/</div>
                        <div class="text-gray-700">第一天</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">序数词</div>
                        <div class="keyword text-lg mb-1">the second floor</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈsekənd flɔːr/</div>
                        <div class="text-gray-700">二楼</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">序数词</div>
                        <div class="keyword text-lg mb-1">the third time</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə θɜːrd taɪm/</div>
                        <div class="text-gray-700">第三次</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">地理名词的用法</h4>
                <p class="text-gray-700 mb-3 leading-7">某些地理名词前需要使用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">海洋河流</div>
                        <div class="keyword text-lg mb-1">the Pacific Ocean</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə pəˈsɪfɪk ˈoʊʃən/</div>
                        <div class="text-gray-700">太平洋</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">山脉</div>
                        <div class="keyword text-lg mb-1">the Rocky Mountains</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈrɑːki ˈmaʊntənz/</div>
                        <div class="text-gray-700">落基山脉</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">乐器前用 the</h4>
                <p class="text-gray-700 mb-3 leading-7">演奏乐器时，乐器名称前使用 <span class="keyword">the</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">乐器</div>
                        <div class="keyword text-lg mb-1">play the piano</div>
                        <div class="text-sm text-gray-600 mb-1">/pleɪ ðə piˈænoʊ/</div>
                        <div class="text-gray-700">弹钢琴</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">乐器</div>
                        <div class="keyword text-lg mb-1">play the guitar</div>
                        <div class="text-sm text-gray-600 mb-1">/pleɪ ðə ɡɪˈtɑːr/</div>
                        <div class="text-gray-700">弹吉他</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">乐器</div>
                        <div class="keyword text-lg mb-1">play the violin</div>
                        <div class="text-sm text-gray-600 mb-1">/pleɪ ðə ˌvaɪəˈlɪn/</div>
                        <div class="text-gray-700">拉小提琴</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 零冠词（不用冠词的情况） -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">零冠词 - 不使用冠词的情况</h3>
            <p class="text-gray-700 mb-4 leading-7">在某些情况下，名词前不需要使用任何冠词，这被称为<span class="keyword">零冠词</span>（Zero Article）。掌握何时不用冠词同样重要。</p>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">复数名词泛指</h4>
                <p class="text-gray-700 mb-3 leading-7">复数名词表示一般概念时不用冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复数泛指</div>
                        <div class="keyword text-lg mb-1">Dogs are loyal animals.</div>
                        <div class="text-sm text-gray-600 mb-1">/dɔːɡz ɑːr ˈlɔɪəl ˈænəməlz/</div>
                        <div class="text-gray-700">狗是忠诚的动物。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复数泛指</div>
                        <div class="keyword text-lg mb-1">Books are important.</div>
                        <div class="text-sm text-gray-600 mb-1">/bʊks ɑːr ɪmˈpɔːrtənt/</div>
                        <div class="text-gray-700">书籍很重要。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">不可数名词泛指</h4>
                <p class="text-gray-700 mb-3 leading-7">不可数名词表示一般概念时不用冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">抽象概念</div>
                        <div class="keyword text-lg mb-1">Love is beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/lʌv ɪz ˈbjuːtəfəl/</div>
                        <div class="text-gray-700">爱是美好的。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">物质名词</div>
                        <div class="keyword text-lg mb-1">Water is essential.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwɔːtər ɪz ɪˈsenʃəl/</div>
                        <div class="text-gray-700">水是必需的。</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学科名词</div>
                        <div class="keyword text-lg mb-1">Mathematics is difficult.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˌmæθəˈmætɪks ɪz ˈdɪfəkəlt/</div>
                        <div class="text-gray-700">数学很难。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">专有名词</h4>
                <p class="text-gray-700 mb-3 leading-7">大多数专有名词前不用冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人名</div>
                        <div class="keyword text-lg mb-1">John is my friend.</div>
                        <div class="text-sm text-gray-600 mb-1">/dʒɑːn ɪz maɪ frend/</div>
                        <div class="text-gray-700">约翰是我的朋友。</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">城市名</div>
                        <div class="keyword text-lg mb-1">New York is big.</div>
                        <div class="text-sm text-gray-600 mb-1">/nuː jɔːrk ɪz bɪɡ/</div>
                        <div class="text-gray-700">纽约很大。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">国家名</div>
                        <div class="keyword text-lg mb-1">China is beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtʃaɪnə ɪz ˈbjuːtəfəl/</div>
                        <div class="text-gray-700">中国很美丽。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">运动项目</h4>
                <p class="text-gray-700 mb-3 leading-7">运动项目名称前不用冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">球类运动</div>
                        <div class="keyword text-lg mb-1">play basketball</div>
                        <div class="text-sm text-gray-600 mb-1">/pleɪ ˈbæskətbɔːl/</div>
                        <div class="text-gray-700">打篮球</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">球类运动</div>
                        <div class="keyword text-lg mb-1">play soccer</div>
                        <div class="text-sm text-gray-600 mb-1">/pleɪ ˈsɑːkər/</div>
                        <div class="text-gray-700">踢足球</div>
                    </div>
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">其他运动</div>
                        <div class="keyword text-lg mb-1">go swimming</div>
                        <div class="text-sm text-gray-600 mb-1">/ɡoʊ ˈswɪmɪŋ/</div>
                        <div class="text-gray-700">去游泳</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 冠词的特殊用法和习语 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">冠词的特殊用法和习语</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">固定搭配中的冠词</h4>
                <p class="text-gray-700 mb-3 leading-7">某些固定搭配中冠词的使用是固定的，需要记忆：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时间表达</div>
                        <div class="keyword text-lg mb-1">in the morning</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪn ðə ˈmɔːrnɪŋ/</div>
                        <div class="text-gray-700">在早上</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时间表达</div>
                        <div class="keyword text-lg mb-1">at night</div>
                        <div class="text-sm text-gray-600 mb-1">/æt naɪt/</div>
                        <div class="text-gray-700">在晚上</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地点表达</div>
                        <div class="keyword text-lg mb-1">go to school</div>
                        <div class="text-sm text-gray-600 mb-1">/ɡoʊ tu skuːl/</div>
                        <div class="text-gray-700">去上学</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地点表达</div>
                        <div class="keyword text-lg mb-1">go to the hospital</div>
                        <div class="text-sm text-gray-600 mb-1">/ɡoʊ tu ðə ˈhɑːspɪtəl/</div>
                        <div class="text-gray-700">去医院</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">身体部位的冠词使用</h4>
                <p class="text-gray-700 mb-3 leading-7">涉及身体部位时，通常使用 <span class="keyword">the</span> 而不是物主代词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">身体部位</div>
                        <div class="keyword text-lg mb-1">He hit me on the head.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi hɪt mi ɑːn ðə hed/</div>
                        <div class="text-gray-700">他打了我的头。</div>
                    </div>
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">身体部位</div>
                        <div class="keyword text-lg mb-1">She took me by the hand.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi tʊk mi baɪ ðə hænd/</div>
                        <div class="text-gray-700">她拉着我的手。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">家庭成员和关系</h4>
                <p class="text-gray-700 mb-3 leading-7">谈论家庭成员时冠词的使用：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">家庭成员</div>
                        <div class="keyword text-lg mb-1">My father is a doctor.</div>
                        <div class="text-sm text-gray-600 mb-1">/maɪ ˈfɑːðər ɪz ə ˈdɑːktər/</div>
                        <div class="text-gray-700">我父亲是医生。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">家庭成员</div>
                        <div class="keyword text-lg mb-1">The mother loves her child.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmʌðər lʌvz hər tʃaɪld/</div>
                        <div class="text-gray-700">母亲爱她的孩子。</div>
                    </div>
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">泛指概念</div>
                        <div class="keyword text-lg mb-1">Father knows best.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈfɑːðər noʊz best/</div>
                        <div class="text-gray-700">父亲最了解。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 冠词使用的常见错误 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">冠词使用的常见错误</h3>
            <p class="text-gray-700 mb-4 leading-7">了解常见的冠词使用错误可以帮助你避免这些问题，提高英语表达的准确性。</p>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">不可数名词的错误用法</h4>
                <p class="text-gray-700 mb-3 leading-7">不可数名词前不能使用 <span class="keyword">a/an</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-500 mb-1">错误用法</div>
                        <div class="text-lg mb-1 line-through">I need an advice.</div>
                        <div class="text-sm text-gray-600 mb-1">❌ 错误</div>
                        <div class="text-gray-700">我需要一个建议。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">正确用法</div>
                        <div class="keyword text-lg mb-1">I need advice.</div>
                        <div class="text-sm text-gray-600 mb-1">✓ 正确</div>
                        <div class="text-gray-700">我需要建议。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">复数名词的错误用法</h4>
                <p class="text-gray-700 mb-3 leading-7">复数名词前不能使用 <span class="keyword">a/an</span>：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-500 mb-1">错误用法</div>
                        <div class="text-lg mb-1 line-through">I like a dogs.</div>
                        <div class="text-sm text-gray-600 mb-1">❌ 错误</div>
                        <div class="text-gray-700">我喜欢狗。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">正确用法</div>
                        <div class="keyword text-lg mb-1">I like dogs.</div>
                        <div class="text-sm text-gray-600 mb-1">✓ 正确</div>
                        <div class="text-gray-700">我喜欢狗。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">专有名词的错误用法</h4>
                <p class="text-gray-700 mb-3 leading-7">大多数专有名词前不需要冠词：</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-500 mb-1">错误用法</div>
                        <div class="text-lg mb-1 line-through">I live in the China.</div>
                        <div class="text-sm text-gray-600 mb-1">❌ 错误</div>
                        <div class="text-gray-700">我住在中国。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">正确用法</div>
                        <div class="keyword text-lg mb-1">I live in China.</div>
                        <div class="text-sm text-gray-600 mb-1">✓ 正确</div>
                        <div class="text-gray-700">我住在中国。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 冠词使用技巧和记忆方法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">冠词使用技巧和记忆方法</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">判断流程图</h4>
                <p class="text-gray-700 mb-4 leading-7">使用以下思维流程来判断是否需要冠词以及使用哪个冠词：</p>
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-gray-700">1. 是否为专有名词？→ 是：通常不用冠词</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-gray-700">2. 是否为不可数名词？→ 是：泛指不用冠词，特指用 the</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                            <span class="text-gray-700">3. 是否为复数名词？→ 是：泛指不用冠词，特指用 the</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                            <span class="text-gray-700">4. 是否为单数可数名词？→ 是：首次提及用 a/an，特指用 the</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">记忆口诀</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">a/an 使用口诀</div>
                        <div class="keyword text-lg mb-1">辅音前面用 a，元音前面用 an</div>
                        <div class="text-sm text-gray-600 mb-1">/kənˈsoʊnənt/ /ˈvaʊəl/</div>
                        <div class="text-gray-700">根据发音而非拼写判断</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">the 使用口诀</div>
                        <div class="keyword text-lg mb-1">特指独一用 the，泛指复数零冠词</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə/ /ði/</div>
                        <div class="text-gray-700">特定事物用 the，一般概念不用冠词</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实际应用中的冠词使用 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">实际应用中的冠词使用</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">日常对话中的冠词</h4>
                <p class="text-gray-700 mb-3 leading-7">在日常对话中正确使用冠词的例子：</p>
                <div class="grid grid-cols-1 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">购物对话</div>
                        <div class="keyword text-lg mb-1">I need a pen and the book you mentioned.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd ə pen ænd ðə bʊk ju ˈmenʃənd/</div>
                        <div class="text-gray-700">我需要一支笔和你提到的那本书。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">介绍对话</div>
                        <div class="keyword text-lg mb-1">She's a teacher at the local school.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiz ə ˈtiːtʃər æt ðə ˈloʊkəl skuːl/</div>
                        <div class="text-gray-700">她是当地学校的一名教师。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">写作中的冠词使用</h4>
                <p class="text-gray-700 mb-3 leading-7">在正式写作中冠词的正确使用：</p>
                <div class="grid grid-cols-1 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学术写作</div>
                        <div class="keyword text-lg mb-1">The research shows that education is important.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsɜːrtʃ ʃoʊz ðæt ˌedʒuˈkeɪʃən ɪz ɪmˈpɔːrtənt/</div>
                        <div class="text-gray-700">研究表明教育很重要。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">商务写作</div>
                        <div class="keyword text-lg mb-1">The company needs an experienced manager.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəni niːdz ən ɪkˈspɪriənst ˈmænədʒər/</div>
                        <div class="text-gray-700">公司需要一位有经验的经理。</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
